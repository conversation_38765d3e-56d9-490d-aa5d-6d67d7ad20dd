<?php

declare(strict_types=1);

namespace Tests\Integration\App\EventSubscriber;

use App\App\EventSubscriber\AppEventSubscriber;
use App\App\Model\AppFactory;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Twig\Environment;

final class AppEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            AppEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }

    public function testRenderTemplateHeaders(): void
    {
        $request = self::createRequest('/');
        $this->setRequest($request);

        $event = new RenderTemplateHeadersEvent();

        /** @var AppFactory $appFactory */
        $appFactory = self::getContainer()->get(AppFactory::class);
        /** @var Environment $twig */
        $twig = self::getContainer()->get(Environment::class);

        $eventSubscriber = new AppEventSubscriber($appFactory, $twig);

        $eventSubscriber->renderTemplateHeaders($event);

        self::assertCount(1, $event->getItems());
    }

    public function testRenderTemplateHeadersOnlyRendersOnce(): void
    {
        $request = self::createRequest('/');
        $this->setRequest($request);

        $event = new RenderTemplateHeadersEvent();

        /** @var AppFactory $appFactory */
        $appFactory = self::getContainer()->get(AppFactory::class);
        /** @var Environment $twig */
        $twig = self::getContainer()->get(Environment::class);

        $eventSubscriber = new AppEventSubscriber($appFactory, $twig);

        // Call the method multiple times
        $eventSubscriber->renderTemplateHeaders($event);
        $eventSubscriber->renderTemplateHeaders($event);
        $eventSubscriber->renderTemplateHeaders($event);

        // Should only have one item despite multiple calls
        self::assertCount(1, $event->getItems());
    }
}
