<?php

declare(strict_types=1);

namespace Tests\Unit\Search\EventSubscriber;

use App\JsonTemplate\View\Data\ViewDataRegistry;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\ViewInterface;
use App\Search\EventSubscriber\SearchEventSubscriber;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\Tracking\Request\SeaRequestStub;

final class SearchEventSubscriberTest extends TestCase
{
    /**
     * @return array<string, array<string, mixed>>
     */
    public static function queryDataProvider(): array
    {
        return [
            'sea query takes precedence' => [
                'seaUserQuery' => 'sea-query',
                'searchQuery' => 'search-query',
                'resultsBlocked' => false,
                'expectedQuery' => 'sea-query',
            ],
            'search query when sea query is null' => [
                'seaUserQuery' => null,
                'searchQuery' => 'search-query',
                'resultsBlocked' => false,
                'expectedQuery' => 'search-query',
            ],
            'null query when results blocked' => [
                'seaUserQuery' => 'sea-query',
                'searchQuery' => 'search-query',
                'resultsBlocked' => true,
                'expectedQuery' => null,
            ],
            'null query when both queries are null' => [
                'seaUserQuery' => null,
                'searchQuery' => null,
                'resultsBlocked' => false,
                'expectedQuery' => null,
            ],
        ];
    }

    #[DataProvider('queryDataProvider')]
    public function testOnJsonTemplateViewCreatedSetsQuery(
        ?string $seaUserQuery,
        ?string $searchQuery,
        bool $resultsBlocked,
        ?string $expectedQuery
    ): void {
        $seaRequest = new SeaRequestStub();
        $seaRequest->setUserQuery($seaUserQuery);

        $searchRequest = new SearchRequestStub();
        $searchRequest->setQuery($searchQuery);

        $blocker = $this->createMock(TrademarkInfringementResultBlocker::class);
        $blocker->method('blockResults')->willReturn($resultsBlocked);

        $dataRequest = new ViewDataRequest();
        $dataRegistry = new ViewDataRegistry();

        $view = $this->createMock(ViewInterface::class);
        $view->method('getDataRequest')->willReturn($dataRequest);
        $view->method('getDataRegistry')->willReturn($dataRegistry);

        $event = new JsonTemplateViewCreatedEvent($view);
        $subscriber = new SearchEventSubscriber($seaRequest, $searchRequest, $blocker);
        $subscriber->onJsonTemplateViewCreated($event);

        self::assertSame($expectedQuery, $dataRequest->getQuery());
        self::assertSame($expectedQuery, $dataRegistry->getQuery());
    }
}
