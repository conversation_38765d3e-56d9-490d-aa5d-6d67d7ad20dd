<?php

declare(strict_types=1);

namespace Tests\Unit\OneTrust\EventSubscriber;

use App\OneTrust\EventSubscriber\InjectOneTrustScriptsEventSubscriber;
use App\OneTrust\Helper\OneTrustHelper;
use App\SplitTest\SplitTestExtendedReaderInterface;
use App\Template\Event\RenderTemplateHeadersEvent;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Twig\Environment;
use Twig\Error\Error as TwigError;

final class InjectOneTrustScriptsEventSubscriberTest extends TestCase
{
    private OneTrustHelper & MockObject $oneTrustHelper;

    private SplitTestExtendedReaderInterface & MockObject $splitTestReader;

    private Environment & MockObject $twig;

    protected function setUp(): void
    {
        parent::setUp();

        $this->oneTrustHelper = $this->createMock(OneTrustHelper::class);
        $this->splitTestReader = $this->createMock(SplitTestExtendedReaderInterface::class);
        $this->twig = $this->createMock(Environment::class);
    }

    /**
     * @return array<string, array<string, mixed>>
     */
    public static function renderTemplateHeadersDataProvider(): array
    {
        return [
            'enabled without split test variants' => [
                'isEnabled' => true,
                'domainScriptId' => 'domain-script-id',
                'splitTestVariantsActive' => false,
                'expectRender' => true,
                'expectedTcfStub' => true,
            ],
            'enabled with split test variants' => [
                'isEnabled' => true,
                'domainScriptId' => 'domain-script-id',
                'splitTestVariantsActive' => true,
                'expectRender' => true,
                'expectedTcfStub' => false,
            ],
            'disabled' => [
                'isEnabled' => false,
                'domainScriptId' => '',
                'splitTestVariantsActive' => false,
                'expectRender' => false,
                'expectedTcfStub' => null,
            ],
            'enabled with empty domain script id' => [
                'isEnabled' => true,
                'domainScriptId' => '',
                'splitTestVariantsActive' => false,
                'expectRender' => true,
                'expectedTcfStub' => true,
            ],
        ];
    }
    #[DataProvider('renderTemplateHeadersDataProvider')]
    public function testRenderTemplateHeaders(
        bool $isEnabled,
        string $domainScriptId,
        bool $splitTestVariantsActive,
        bool $expectRender,
        ?bool $expectedTcfStub
    ): void {
        $this->oneTrustHelper->method('isEnabled')->willReturn($isEnabled);
        $this->oneTrustHelper->method('getDomainScriptId')->willReturn($domainScriptId);
        $this->splitTestReader->method('isOneOfVariantsActive')
            ->with(['enc', 'encwi'])
            ->willReturn($splitTestVariantsActive);

        if ($expectRender) {
            $this->twig->expects($this->once())
                ->method('render')
                ->with(
                    '@theme/onetrust/onetrust_script_html.html.twig',
                    [
                        'domain_script_id' => $domainScriptId,
                        'add_tcf_stub'     => $expectedTcfStub,
                    ]
                )
                ->willReturn('rendered-content');
        } else {
            $this->twig->expects($this->never())->method('render');
        }

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectOneTrustScriptsEventSubscriber(
            $this->oneTrustHelper,
            $this->splitTestReader,
            $this->twig
        );
        $subscriber->renderTemplateHeaders($event);

        if ($expectRender) {
            self::assertCount(1, $event->getItems());
            self::assertSame('rendered-content', $event->getItems()[0]);
        } else {
            self::assertEmpty($event->getItems());
        }
    }

    /**
     * Tests that Twig rendering exceptions are properly handled.
     */
    public function testRenderTemplateHeadersHandlesTwigException(): void
    {
        $this->oneTrustHelper->method('isEnabled')->willReturn(true);
        $this->oneTrustHelper->method('getDomainScriptId')->willReturn('domain-script-id');
        $this->splitTestReader->method('isOneOfVariantsActive')->willReturn(false);

        $this->twig->method('render')->willThrowException(new TwigError('Template error'));

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectOneTrustScriptsEventSubscriber(
            $this->oneTrustHelper,
            $this->splitTestReader,
            $this->twig
        );

        $this->expectException(TwigError::class);
        $this->expectExceptionMessage('Template error');

        $subscriber->renderTemplateHeaders($event);
    }
}
